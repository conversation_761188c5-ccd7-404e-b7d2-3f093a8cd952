/* karla-regular - latin */
@font-face {
	font-family: 'Karla';
	font-style: normal;
	font-weight: 400;
	src: local(''),
		 url('karla-v23-latin-regular.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('karla-v23-latin-regular.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* karla-700 - latin */
  @font-face {
	font-family: 'Karla';
	font-style: normal;
	font-weight: 700;
	src: local(''),
		 url('karla-v23-latin-700.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('karla-v23-latin-700.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* karla-italic - latin */
  @font-face {
	font-family: 'Karla';
	font-style: italic;
	font-weight: 400;
	src: local(''),
		 url('karla-v23-latin-italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('karla-v23-latin-italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* karla-700italic - latin */
  @font-face {
	font-family: 'Karla';
	font-style: italic;
	font-weight: 700;
	src: local(''),
		 url('karla-v23-latin-700italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('karla-v23-latin-700italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
.font-karla{
	font-family: 'Karla';
}