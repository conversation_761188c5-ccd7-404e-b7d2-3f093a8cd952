/*!
 * Copyright (c) 2021 Momo Bassit.
 * Licensed under the MIT License (MIT)
 * https://github.com/mdbassit/Coloris
 */
!function(u,p,s){var d,f,h,b,c,v,y,i,g,l,m,w,k,x,a,r=p.createElement("canvas").getContext("2d"),L={r:0,g:0,b:0,h:0,s:0,v:0,a:1},E={el:"[data-coloris]",parent:"body",theme:"default",themeMode:"light",wrap:!0,margin:2,format:"hex",formatToggle:!1,swatches:[],swatchesOnly:!1,alpha:!0,forceAlpha:!1,focusInput:!0,selectInput:!1,inline:!1,defaultColor:"#000000",clearButton:!1,clearLabel:"Clear",a11y:{open:"Open color picker",close:"Close color picker",marker:"Saturation: {s}. Brightness: {v}.",hueSlider:"Hue slider",alphaSlider:"Opacity slider",input:"Color value field",format:"Color format",swatch:"Color swatch",instruction:"Saturation and brightness selector. Use up, down, left and right arrow keys to select."}},n={},o="",S={},A=!1;function T(e){if("object"==typeof e)for(var t in e)switch(t){case"el":H(e.el),!1!==e.wrap&&N(e.el);break;case"parent":(d=p.querySelector(e.parent))&&(d.appendChild(f),E.parent=e.parent,d===p.body&&(d=null));break;case"themeMode":E.themeMode=e.themeMode,"auto"===e.themeMode&&u.matchMedia&&u.matchMedia("(prefers-color-scheme: dark)").matches&&(E.themeMode="dark");case"theme":e.theme&&(E.theme=e.theme),f.className="clr-picker clr-"+E.theme+" clr-"+E.themeMode,E.inline&&O();break;case"margin":e.margin*=1,E.margin=(isNaN(e.margin)?E:e).margin;break;case"wrap":e.el&&e.wrap&&N(e.el);break;case"formatToggle":E.formatToggle=!!e.formatToggle,X("clr-format").style.display=E.formatToggle?"block":"none",E.formatToggle&&(E.format="auto");break;case"swatches":Array.isArray(e.swatches)&&function(){var a=[];e.swatches.forEach(function(e,t){a.push('<button type="button" id="clr-swatch-'+t+'" aria-labelledby="clr-swatch-label clr-swatch-'+t+'" style="color: '+e+';">'+e+"</button>")}),X("clr-swatches").innerHTML=a.length?"<div>"+a.join("")+"</div>":"",E.swatches=e.swatches.slice()}();break;case"swatchesOnly":E.swatchesOnly=!!e.swatchesOnly,f.setAttribute("data-minimal",E.swatchesOnly);break;case"alpha":E.alpha=!!e.alpha,f.setAttribute("data-alpha",E.alpha);break;case"inline":E.inline=!!e.inline,f.setAttribute("data-inline",E.inline),E.inline&&(l=e.defaultColor||E.defaultColor,x=D(l),O(),j(l));break;case"clearButton":"object"==typeof e.clearButton&&(e.clearButton.label&&(E.clearLabel=e.clearButton.label,i.innerHTML=E.clearLabel),e.clearButton=e.clearButton.show),E.clearButton=!!e.clearButton,i.style.display=E.clearButton?"block":"none";break;case"clearLabel":E.clearLabel=e.clearLabel,i.innerHTML=E.clearLabel;break;case"a11y":var a,l,r=e.a11y,n=!1;if("object"==typeof r)for(var o in r)r[o]&&E.a11y[o]&&(E.a11y[o]=r[o],n=!0);n&&(a=X("clr-open-label"),l=X("clr-swatch-label"),a.innerHTML=E.a11y.open,l.innerHTML=E.a11y.swatch,v.setAttribute("aria-label",E.a11y.close),g.setAttribute("aria-label",E.a11y.hueSlider),m.setAttribute("aria-label",E.a11y.alphaSlider),y.setAttribute("aria-label",E.a11y.input),h.setAttribute("aria-label",E.a11y.instruction));default:E[t]=e[t]}}function C(e,t){"string"==typeof e&&"object"==typeof t&&(n[e]=t,A=!0)}function M(e){delete n[e],0===Object.keys(n).length&&(A=!1,e===o&&B())}function t(l){if(A){var e,r=["el","wrap","inline","defaultColor","a11y"];for(e in n)if("break"===function(e){var t=n[e];if(l.matches(e)){for(var a in o=e,S={},r.forEach(function(e){return delete t[e]}),t)S[a]=Array.isArray(E[a])?E[a].slice():E[a];return T(t),"break"}}(e))break}}function B(){0<Object.keys(S).length&&(T(S),o="",S={})}function H(e){U(p,"click",e,function(e){E.inline||(t(e.target),k=e.target,a=k.value,x=D(a),f.classList.add("clr-open"),O(),j(a),(E.focusInput||E.selectInput)&&y.focus({preventScroll:!0}),E.selectInput&&y.select(),k.dispatchEvent(new Event("open",{bubbles:!0})))}),U(p,"input",e,function(e){var t=e.target.parentNode;t.classList.contains("clr-field")&&(t.style.color=e.target.value)})}function O(){var e,t,a,l,r=d,n=u.scrollY,o=f.offsetWidth,i=f.offsetHeight,c={left:!1,top:!1},s={x:0,y:0};r&&(a=u.getComputedStyle(r),e=parseFloat(a.marginTop),l=parseFloat(a.borderTopWidth),(s=r.getBoundingClientRect()).y+=l+n),E.inline||(a=(t=k.getBoundingClientRect()).x,l=n+t.y+t.height+E.margin,r?(a-=s.x,l-=s.y,a+o>r.clientWidth&&(a+=t.width-o,c.left=!0),l+i>r.clientHeight-e&&i+E.margin<=t.top-(s.y-n)&&(l-=t.height+i+2*E.margin,c.top=!0),l+=r.scrollTop):(a+o>p.documentElement.clientWidth&&(a+=t.width-o,c.left=!0),l+i-n>p.documentElement.clientHeight&&i+E.margin<=t.top&&(l=n+t.y-i-E.margin,c.top=!0)),f.classList.toggle("clr-left",c.left),f.classList.toggle("clr-top",c.top),f.style.left=a+"px",f.style.top=l+"px"),b={width:h.offsetWidth,height:h.offsetHeight,x:f.offsetLeft+h.offsetLeft+s.x,y:f.offsetTop+h.offsetTop+s.y}}function N(e){p.querySelectorAll(e).forEach(function(e){var t,a=e.parentNode;a.classList.contains("clr-field")||((t=p.createElement("div")).innerHTML='<button type="button" aria-labelledby="clr-open-label"></button>',a.insertBefore(t,e),t.setAttribute("class","clr-field"),t.style.color=e.value,t.appendChild(e))})}function I(e){var t;k&&!E.inline&&(t=k,e&&(k=null,a!==t.value&&(t.value=a,t.dispatchEvent(new Event("input",{bubbles:!0})))),setTimeout(function(){a!==t.value&&t.dispatchEvent(new Event("change",{bubbles:!0}))}),f.classList.remove("clr-open"),A&&B(),t.dispatchEvent(new Event("close",{bubbles:!0})),E.focusInput&&t.focus({preventScroll:!0}),k=null)}function j(e){var t=function(e){var t;r.fillStyle="#000",r.fillStyle=e,(e=/^((rgba)|rgb)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]*?([\d.]+|$)/i.exec(r.fillStyle))?(t={r:+e[3],g:+e[4],b:+e[5],a:+e[6]}).a=+t.a.toFixed(2):(e=r.fillStyle.replace("#","").match(/.{2}/g).map(function(e){return parseInt(e,16)}),t={r:e[0],g:e[1],b:e[2],a:1});return t}(e),e=function(e){var t=e.r/255,a=e.g/255,l=e.b/255,r=s.max(t,a,l),n=s.min(t,a,l),o=r-n,i=r,c=0,n=0;o&&(r===t&&(c=(a-l)/o),r===a&&(c=2+(l-t)/o),r===l&&(c=4+(t-a)/o),r&&(n=o/r));return{h:(c=s.floor(60*c))<0?c+360:c,s:s.round(100*n),v:s.round(100*i),a:e.a}}(t);R(e.s,e.v),q(t,e),g.value=e.h,f.style.color="hsl("+e.h+", 100%, 50%)",l.style.left=e.h/360*100+"%",c.style.left=b.width*e.s/100+"px",c.style.top=b.height-b.height*e.v/100+"px",m.value=100*e.a,w.style.left=100*e.a+"%"}function D(e){e=e.substring(0,3).toLowerCase();return"rgb"===e||"hsl"===e?e:"hex"}function F(e){e=void 0!==e?e:y.value,k&&(k.value=e,k.dispatchEvent(new Event("input",{bubbles:!0}))),p.dispatchEvent(new CustomEvent("coloris:pick",{detail:{color:e}}))}function W(e,t){e={h:+g.value,s:e/b.width*100,v:100-t/b.height*100,a:m.value/100},t=function(e){var t=e.s/100,a=e.v/100,l=t*a,r=e.h/60,n=l*(1-s.abs(r%2-1)),o=a-l;l+=o,n+=o;t=s.floor(r)%6,a=[l,n,o,o,n,l][t],r=[n,l,l,n,o,o][t],t=[o,o,n,l,l,n][t];return{r:s.round(255*a),g:s.round(255*r),b:s.round(255*t),a:e.a}}(e);R(e.s,e.v),q(t,e),F()}function R(e,t){var a=E.a11y.marker;e=+e.toFixed(1),t=+t.toFixed(1),a=(a=a.replace("{s}",e)).replace("{v}",t),c.setAttribute("aria-label",a)}function Y(e){var t={pageX:((a=e).changedTouches?a.changedTouches[0]:a).pageX,pageY:(a.changedTouches?a.changedTouches[0]:a).pageY},a=t.pageX-b.x,t=t.pageY-b.y;d&&(t+=d.scrollTop),a=a<0?0:a>b.width?b.width:a,t=t<0?0:t>b.height?b.height:t,c.style.left=a+"px",c.style.top=t+"px",W(a,t),e.preventDefault(),e.stopPropagation()}function q(e,t){void 0===t&&(t={});var a,l,r=E.format;for(a in e=void 0===e?{}:e)L[a]=e[a];for(l in t)L[l]=t[l];var n,o=function(e){var t=e.r.toString(16),a=e.g.toString(16),l=e.b.toString(16),r="";e.r<16&&(t="0"+t);e.g<16&&(a="0"+a);e.b<16&&(l="0"+l);E.alpha&&(e.a<1||E.forceAlpha)&&(e=255*e.a|0,r=e.toString(16),e<16&&(r="0"+r));return"#"+t+a+l+r}(L),i=o.substring(0,7);switch(c.style.color=i,w.parentNode.style.color=i,w.style.color=o,v.style.color=o,h.style.display="none",h.offsetHeight,h.style.display="",w.nextElementSibling.style.display="none",w.nextElementSibling.offsetHeight,w.nextElementSibling.style.display="","mixed"===r?r=1===L.a?"hex":"rgb":"auto"===r&&(r=x),r){case"hex":y.value=o;break;case"rgb":y.value=(n=L,!E.alpha||1===n.a&&!E.forceAlpha?"rgb("+n.r+", "+n.g+", "+n.b+")":"rgba("+n.r+", "+n.g+", "+n.b+", "+n.a+")");break;case"hsl":y.value=(n=function(e){var t,a=e.v/100,l=a*(1-e.s/100/2);0<l&&l<1&&(t=s.round((a-l)/s.min(l,1-l)*100));return{h:e.h,s:t||0,l:s.round(100*l),a:e.a}}(L),!E.alpha||1===n.a&&!E.forceAlpha?"hsl("+n.h+", "+n.s+"%, "+n.l+"%)":"hsla("+n.h+", "+n.s+"%, "+n.l+"%, "+n.a+")")}p.querySelector('.clr-format [value="'+r+'"]').checked=!0}function e(){var e=+g.value,t=+c.style.left.replace("px",""),a=+c.style.top.replace("px","");f.style.color="hsl("+e+", 100%, 50%)",l.style.left=e/360*100+"%",W(t,a)}function P(){var e=m.value/100;w.style.left=100*e+"%",q({a:e}),F()}function X(e){return p.getElementById(e)}function U(e,t,a,l){var r=Element.prototype.matches||Element.prototype.msMatchesSelector;"string"==typeof a?e.addEventListener(t,function(e){r.call(e.target,a)&&l.call(e.target,e)}):(l=a,e.addEventListener(t,l))}function G(e,t){t=void 0!==t?t:[],"loading"!==p.readyState?e.apply(void 0,t):p.addEventListener("DOMContentLoaded",function(){e.apply(void 0,t)})}void 0!==NodeList&&NodeList.prototype&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),u.Coloris=function(){var r={set:T,wrap:N,close:I,setInstance:C,removeInstance:M,updatePosition:O};function e(e){G(function(){e&&("string"==typeof e?H:T)(e)})}for(var t in r)!function(l){e[l]=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];G(r[l],t)}}(t);return e}(),G(function(){d=null,(f=p.createElement("div")).setAttribute("id","clr-picker"),f.className="clr-picker",f.innerHTML='<input id="clr-color-value" class="clr-color" type="text" value="" spellcheck="false" aria-label="'+E.a11y.input+'"><div id="clr-color-area" class="clr-gradient" role="application" aria-label="'+E.a11y.instruction+'"><div id="clr-color-marker" class="clr-marker" tabindex="0"></div></div><div class="clr-hue"><input id="clr-hue-slider" type="range" min="0" max="360" step="1" aria-label="'+E.a11y.hueSlider+'"><div id="clr-hue-marker"></div></div><div class="clr-alpha"><input id="clr-alpha-slider" type="range" min="0" max="100" step="1" aria-label="'+E.a11y.alphaSlider+'"><div id="clr-alpha-marker"></div><span></span></div><div id="clr-format" class="clr-format"><fieldset class="clr-segmented"><legend>'+E.a11y.format+'</legend><input id="clr-f1" type="radio" name="clr-format" value="hex"><label for="clr-f1">Hex</label><input id="clr-f2" type="radio" name="clr-format" value="rgb"><label for="clr-f2">RGB</label><input id="clr-f3" type="radio" name="clr-format" value="hsl"><label for="clr-f3">HSL</label><span></span></fieldset></div><div id="clr-swatches" class="clr-swatches"></div><button type="button" id="clr-clear" class="clr-clear">'+E.clearLabel+'</button><button type="button" id="clr-color-preview" class="clr-preview" aria-label="'+E.a11y.close+'"></button><span id="clr-open-label" hidden>'+E.a11y.open+'</span><span id="clr-swatch-label" hidden>'+E.a11y.swatch+"</span>",p.body.appendChild(f),h=X("clr-color-area"),c=X("clr-color-marker"),i=X("clr-clear"),v=X("clr-color-preview"),y=X("clr-color-value"),g=X("clr-hue-slider"),l=X("clr-hue-marker"),m=X("clr-alpha-slider"),w=X("clr-alpha-marker"),H(E.el),N(E.el),U(f,"mousedown",function(e){f.classList.remove("clr-keyboard-nav"),e.stopPropagation()}),U(h,"mousedown",function(e){U(p,"mousemove",Y)}),U(h,"touchstart",function(e){p.addEventListener("touchmove",Y,{passive:!1})}),U(c,"mousedown",function(e){U(p,"mousemove",Y)}),U(c,"touchstart",function(e){p.addEventListener("touchmove",Y,{passive:!1})}),U(y,"change",function(e){(k||E.inline)&&(j(y.value),F())}),U(i,"click",function(e){F(""),I()}),U(v,"click",function(e){F(),I()}),U(p,"click",".clr-format input",function(e){x=e.target.value,q(),F()}),U(f,"click",".clr-swatches button",function(e){j(e.target.textContent),F(),E.swatchesOnly&&I()}),U(p,"mouseup",function(e){p.removeEventListener("mousemove",Y)}),U(p,"touchend",function(e){p.removeEventListener("touchmove",Y)}),U(p,"mousedown",function(e){f.classList.remove("clr-keyboard-nav"),I()}),U(p,"keydown",function(e){"Escape"===e.key?I(!0):"Tab"===e.key&&f.classList.add("clr-keyboard-nav")}),U(p,"click",".clr-field button",function(e){A&&B(),e.target.nextElementSibling.dispatchEvent(new Event("click",{bubbles:!0}))}),U(c,"keydown",function(e){var t={ArrowUp:[0,-1],ArrowDown:[0,1],ArrowLeft:[-1,0],ArrowRight:[1,0]};-1!==Object.keys(t).indexOf(e.key)&&(!function(e,t){e=+c.style.left.replace("px","")+e,t=+c.style.top.replace("px","")+t,c.style.left=e+"px",c.style.top=t+"px",W(e,t)}.apply(void 0,t[e.key]),e.preventDefault())}),U(h,"click",Y),U(g,"input",e),U(m,"input",P)})}(window,document,Math);