const{ClassicEditor:ClassicEditor,AccessibilityHelp:AccessibilityHelp,Alignment:Alignment,Autoformat:Autoformat,AutoImage:AutoImage,AutoLink:AutoLink,Autosave:Autosave,BalloonToolbar:BalloonToolbar,Base64UploadAdapter:Base64UploadAdapter,BlockQuote:BlockQuote,Bold:Bold,Code:Code,CodeBlock:CodeBlock,Essentials:Essentials,FontBackgroundColor:FontBackgroundColor,FontColor:FontColor,FontFamily:FontFamily,FontSize:FontSize,FullPage:FullPage,GeneralHtmlSupport:GeneralHtmlSupport,Heading:Heading,Highlight:Highlight,HorizontalLine:HorizontalLine,HtmlComment:HtmlComment,HtmlEmbed:HtmlEmbed,ImageBlock:ImageBlock,ImageCaption:ImageCaption,ImageInline:ImageInline,ImageInsert:ImageInsert,ImageInsertViaUrl:ImageInsertViaUrl,ImageResize:ImageResize,ImageStyle:ImageStyle,ImageTextAlternative:ImageTextAlternative,ImageToolbar:ImageToolbar,ImageUpload:ImageUpload,Indent:Indent,IndentBlock:IndentBlock,Italic:Italic,Link:Link,LinkImage:LinkImage,List:List,ListProperties:ListProperties,MediaEmbed:MediaEmbed,PageBreak:PageBreak,Paragraph:Paragraph,RemoveFormat:RemoveFormat,SelectAll:SelectAll,ShowBlocks:ShowBlocks,SourceEditing:SourceEditing,SpecialCharacters:SpecialCharacters,SpecialCharactersArrows:SpecialCharactersArrows,SpecialCharactersCurrency:SpecialCharactersCurrency,SpecialCharactersEssentials:SpecialCharactersEssentials,SpecialCharactersLatin:SpecialCharactersLatin,SpecialCharactersMathematical:SpecialCharactersMathematical,SpecialCharactersText:SpecialCharactersText,Strikethrough:Strikethrough,Style:Style,Subscript:Subscript,Superscript:Superscript,Table:Table,TableCaption:TableCaption,TableCellProperties:TableCellProperties,TableColumnResize:TableColumnResize,TableProperties:TableProperties,TableToolbar:TableToolbar,TextTransformation:TextTransformation,TodoList:TodoList,Underline:Underline,Undo:Undo}=CKEDITOR,editorConfig={toolbar:{items:["bold","italic","underline","strikethrough","|","heading","|","fontColor","fontBackgroundColor","fontSize","fontFamily","highlight","|","alignment","outdent","indent","bulletedList","numberedList","todoList","blockQuote","horizontalLine","|","link","insertImage","mediaEmbed","insertTable","htmlEmbed","sourceEditing","removeFormat","undo","redo"],shouldNotGroupWhenFull:!1},plugins:[AccessibilityHelp,Alignment,Autoformat,AutoImage,AutoLink,Autosave,BalloonToolbar,Base64UploadAdapter,BlockQuote,Bold,Code,CodeBlock,Essentials,FontBackgroundColor,FontColor,FontFamily,FontSize,FullPage,GeneralHtmlSupport,Heading,Highlight,HorizontalLine,HtmlComment,HtmlEmbed,ImageBlock,ImageCaption,ImageInline,ImageInsert,ImageInsertViaUrl,ImageResize,ImageStyle,ImageTextAlternative,ImageToolbar,ImageUpload,Indent,IndentBlock,Italic,Link,LinkImage,List,ListProperties,MediaEmbed,PageBreak,Paragraph,RemoveFormat,SelectAll,ShowBlocks,SourceEditing,SpecialCharacters,SpecialCharactersArrows,SpecialCharactersCurrency,SpecialCharactersEssentials,SpecialCharactersLatin,SpecialCharactersMathematical,SpecialCharactersText,Strikethrough,Style,Subscript,Superscript,Table,TableCaption,TableCellProperties,TableColumnResize,TableProperties,TableToolbar,TextTransformation,TodoList,Underline,Undo],balloonToolbar:["bold","italic","|","link","insertImage","|","bulletedList","numberedList"],fontFamily:{supportAllValues:!0},fontSize:{options:[10,12,14,"default",18,20,22],supportAllValues:!0},heading:{options:[{model:"paragraph",title:"Paragraph",class:"ck-heading_paragraph"},{model:"heading1",view:"h1",title:"Heading 1",class:"ck-heading_heading1"},{model:"heading2",view:"h2",title:"Heading 2",class:"ck-heading_heading2"},{model:"heading3",view:"h3",title:"Heading 3",class:"ck-heading_heading3"},{model:"heading4",view:"h4",title:"Heading 4",class:"ck-heading_heading4"},{model:"heading5",view:"h5",title:"Heading 5",class:"ck-heading_heading5"},{model:"heading6",view:"h6",title:"Heading 6",class:"ck-heading_heading6"}]},htmlSupport:{allow:[{name:/^.*$/,styles:!0,attributes:!0,classes:!0}]},image:{toolbar:["toggleImageCaption","imageTextAlternative","|","imageStyle:inline","imageStyle:wrapText","imageStyle:breakText","|","resizeImage"]},link:{addTargetToExternalLinks:!0,defaultProtocol:"https://",decorators:{openInNewTab:{mode:"manual",label:"Open in a new tab",attributes:{target:"_blank",rel:"noopener noreferrer"}}}},list:{properties:{styles:!0,startIndex:!0,reversed:!0}},mediaEmbed:{previewsInData:!0},placeholder:"Type or paste your content here!",table:{contentToolbar:["tableColumn","tableRow","mergeTableCells","tableProperties","tableCellProperties"]}};