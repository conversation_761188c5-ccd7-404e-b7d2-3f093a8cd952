"use strict";function livesearch(){let t=$("[data-trigger=faqsearchbox]").val();$(".accordion").each(function(){$(this).text().toLowerCase().includes(t.toLowerCase())?$(this).removeClass("d-none"):$(this).addClass("d-none")})}let path=location.pathname.substring(1);path&&$('.nav-link[href$="'+path+'"]').addClass("active"),$(".code-selector").hide(),$(".code-selector[data-id=curl]").fadeIn("fast"),$(".code-lang a").click(function(t){t.preventDefault();let e=$(this).attr("href");$(".code-lang a").removeClass("active"),$("a[href='"+e+"']").addClass("active"),$(".code-selector").hide();let a=e.replace("#","");$(".code-selector[data-id='"+a+"']").fadeIn()}),$("[data-toggle=multibuttons] a").click(function(t){t.preventDefault();let e=$(this).attr("href"),a=$(this).data("bs-parent");$(a).find(".collapse").removeClass("show"),$(a).find(e).addClass("show"),$(this).parent("[data-toggle=multibuttons]").find(".active").attr("class","btn flex-fill"),$(this).attr("class","btn flex-fill shadow-sm bg-white border rounded-pill fw-bold active")}),$("[data-toggle=customertype]").click(function(){$(this).parents(".btn-stack").find(".active").removeClass("active"),"business"==$(this).val()?$("#company").removeClass("d-none"):$("#company").addClass("d-none"),$(this).parent("label").addClass("active")});var Coupon=function(){function t(t){t.next(".form-text").remove(),$.ajax({type:"GET",url:t.data("url"),data:"code="+t.val()+"&country="+$("#country").val(),success:function(e){e.error?(t.addClass("is-invalid"),t.after('<p class="form-text text-sm text-danger">'+e.message+"</p>")):(t.removeClass("is-invalid"),t.after("<p class='form-text text-sm text-success'>"+e.message+"</p>"),o.text(e.discount).parents(".collapse").addClass("show"),n.text(e.newprice),e.tax&&$("#taxamount").text(e.tax))}})}var e=$("#coupon"),a=$("[data-trigger=applycoupon]"),o=$("#discount"),n=$("#total");e.length>0&&a.click(function(a){a.preventDefault(),t(e)})}(),SvgInjector=function(){function t(t){var e={};SVGInjector(t,e,function(t){a=!0})}var e=document.querySelectorAll("img.svg-inject"),a=!1;return e.length&&t(e),{status:a}}(),Tooltip=function(){function t(){e.tooltip()}var e=$('[data-toggle="tooltip"]');e.length&&t()}(),Dropdown=function(){function t(t){var e=t.find(".dropdown-menu");e.addClass("hide"),setTimeout(function(){e.removeClass("hide")},300)}function e(t){t.next().hasClass("show")||t.parents(".dropdown-menu").first().find(".show").removeClass("show");var e=t.next(".dropdown-menu");e.toggleClass("show"),e.parent().toggleClass("show"),t.parents(".nav-item.dropdown.show").on("hidden.bs.dropdown",function(t){$(".dropdown-submenu .show").removeClass("show")})}var a=$(".dropdown-animate"),o=$('.dropdown-submenu [data-toggle="dropdown"]');a.length&&a.on({"hide.bs.dropdown":function(e){t($(this))}}),o.length&&o.on("click",function(t){return e($(this)),!1}),$(".dropdown-menu").on("click",function(t){var e=$._data(document,"events")||{};e=e.click||[];for(var a=0;a<e.length;a++)e[a].selector&&($(t.target).is(e[a].selector)&&e[a].handler.call(t.target,t),$(t.target).parents(e[a].selector).each(function(){e[a].handler.call(this,t)}));t.stopPropagation()})}(),PasswordText=function(){function t(t){var e=$(t.data("target"));return"password"==e.attr("type")?e.attr("type","text"):e.attr("type","password"),!1}var e=$('[data-toggle="password-text"]');e.length&&e.on("click",function(e){t($(this))})}(),Pricing=function(){function t(t){var e=t.data("pricing"),a=t.parents(".pricing-container"),o=$("."+a.attr("class")+" [data-pricing-"+e+"]");$("[data-toggle=pricingterm]").text($("[data-toggle=pricingterm]").data("term-"+e)),t.hasClass("btn-primary")||($("."+a.attr("class")+" button[data-pricing]").removeClass("btn-primary").addClass("btn-light"),t.removeClass("btn-light").addClass("btn-primary"),o.each(function(){var t=$(this).data("pricing-"+e),a=$(this).find("span.price").text();$(this).find("span.price").text(t),$(this).data("pricing-value",a)}),$("a.checkout").each(function(){let t=$(this).attr("href").split("/");"#"!=t&&(t.pop(),$(this).attr("href",t.join("/")+"/"+e))}))}var e=$(".pricing-container"),a=$(".pricing-container button[data-pricing]");e.length&&a.on({click:function(){t($(this))}})}(),ScrollTo=function(){function t(t){$("html, body").animate({scrollTop:$(t).offset().top},"slow")}function e(t){var e=t.attr("href"),a=t.data("scroll-to-offset")?t.data("scroll-to-offset"):0,o={scrollTop:$(e).offset().top-a};$("html, body").stop(!0,!0).animate(o,300),window.location.hash=e,event.preventDefault()}var a=$(".scroll-me, [data-scroll-to], .toc-entry a"),o=window.location.hash;a.length&&a.on("click",function(t){e($(this))}),$(window).on("load",function(){o&&"#!"!=o&&$(o).length&&t(o)})}(),Select=function(){function t(t){var e={};t.select2(e)}var e=$('[data-toggle="select"]');e.length&&e.each(function(){t($(this))})}();if($("#cookieconsent-script").length>0){var cc=initCookieConsent();cc.run({current_lang:"app",autoclear_cookies:!0,cookie_name:"cc_cookie",cookie_expiration:365,page_scripts:!0,autorun:!0,languages:{app:{consent_modal:{title:lang.cookie.title,description:lang.cookie.description+lang.cookie.button,primary_btn:{text:lang.cookie.accept_all,role:"accept_all"},secondary_btn:{text:lang.cookie.accept_necessary,role:"accept_necessary"}},settings_modal:{title:lang.cookie.title,save_settings_btn:lang.cookie.save,accept_all_btn:lang.cookie.accept_all,reject_all_btn:lang.cookie.accept_necessary,close_btn_label:lang.cookie.close,blocks:[{description:lang.cookie.description},{title:lang.cookie.necessary.title,description:lang.cookie.necessary.description,toggle:{value:"necessary",enabled:!0,readonly:!0}},{title:lang.cookie.analytics.title,description:lang.cookie.analytics.description,toggle:{value:"analytics",enabled:!1,readonly:!1}},{title:lang.cookie.ads.title,description:lang.cookie.ads.description,toggle:{value:"ads",enabled:!1,readonly:!1}},{title:lang.cookie.extra.title,description:lang.cookie.extra.description,toggle:{value:"extra",enabled:!1,readonly:!1}},{title:lang.cookie.privacy.title,description:lang.cookie.privacy.description}]}}}})}$(document).ready(()=>{let t;$(document).on("keyup","[data-trigger=faqsearchbox]",()=>{clearTimeout(t),t=setTimeout(livesearch,500)})});