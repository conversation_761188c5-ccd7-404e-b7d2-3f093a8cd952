/* proza-libre-regular - latin */
@font-face {
	font-family: 'Proza Libre';
	font-style: normal;
	font-weight: 400;
	src: local(''),
		 url('proza-libre-v9-latin-regular.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('proza-libre-v9-latin-regular.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* proza-libre-italic - latin */
  @font-face {
	font-family: 'Proza Libre';
	font-style: italic;
	font-weight: 400;
	src: local(''),
		 url('proza-libre-v9-latin-italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('proza-libre-v9-latin-italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, I<PERSON> 9+, Safari 5.1+ */
  }
  /* proza-libre-700 - latin */
  @font-face {
	font-family: 'Proza Libre';
	font-style: normal;
	font-weight: 700;
	src: local(''),
		 url('proza-libre-v9-latin-700.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('proza-libre-v9-latin-700.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* proza-libre-700italic - latin */
  @font-face {
	font-family: 'Proza Libre';
	font-style: italic;
	font-weight: 700;
	src: local(''),
		 url('proza-libre-v9-latin-700italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('proza-libre-v9-latin-700italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
.font-proza_libre{
	font-family: 'Proza Libre';
}