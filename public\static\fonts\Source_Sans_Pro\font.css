/* source-code-pro-regular - latin */
@font-face {
	font-family: 'Source Code Pro';
	font-style: normal;
	font-weight: 400;
	src: local(''),
		 url('source-code-pro-v22-latin-regular.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('source-code-pro-v22-latin-regular.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* source-code-pro-700 - latin */
  @font-face {
	font-family: 'Source Code Pro';
	font-style: normal;
	font-weight: 700;
	src: local(''),
		 url('source-code-pro-v22-latin-700.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('source-code-pro-v22-latin-700.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* source-code-pro-italic - latin */
  @font-face {
	font-family: 'Source Code Pro';
	font-style: italic;
	font-weight: 400;
	src: local(''),
		 url('source-code-pro-v22-latin-italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('source-code-pro-v22-latin-italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
  /* source-code-pro-700italic - latin */
  @font-face {
	font-family: 'Source Code Pro';
	font-style: italic;
	font-weight: 700;
	src: local(''),
		 url('source-code-pro-v22-latin-700italic.woff2') format('woff2'), /* Chrome 26+, Opera 23+, Firefox 39+ */
		 url('source-code-pro-v22-latin-700italic.woff') format('woff'); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
  }
.font-source_sans_pro{
	font-family: 'Source Sans Pro';
}